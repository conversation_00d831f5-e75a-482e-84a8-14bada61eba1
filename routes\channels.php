<?php

use Illuminate\Support\Facades\Broadcast;
use App\Models\Proposal;
use Illuminate\Support\Facades\Log;

Broadcast::channel('proposal.{chat_id}', function ($user, $chat_id) {
    // Log::info('User accessing the channel: ' . $user);
    $proposal = Proposal::where('encrypted_id', $chat_id)->first();
    Log::info('Checking channel authorization. User: ' . $user->id . ' Proposal ID: ' . $proposal->id);
    return ($proposal && (($user->role == '2' && $user->can('view-chats')) || $user->id == $proposal->provider_user_id || $user->id == $proposal->customer_user_id));
}, ['guards' => ['web', 'api']]);
