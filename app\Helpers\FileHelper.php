<?php

namespace App\Helpers;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class FileHelper
{
    public static function uploadFiles($files, $directory = 'uploads', $disk = 'public')
    {
        $filePaths = [];

        if (is_array($files)) {
            foreach ($files as $file) {
                if ($file instanceof UploadedFile) {
                    $filePaths[] = 'storage/' . $file->store($directory, $disk);
                }
            }
        } else {
            if ($files instanceof UploadedFile) {
                $filePaths[] = 'storage/' . $files->store($directory, $disk);
            }
        }

        return count($filePaths) === 1 ? $filePaths[0] : $filePaths;
    }

    public static function deleteFile($filePath, $disk = 'public')
    {
        if (Storage::disk($disk)->exists($filePath)) {
            Storage::disk($disk)->delete($filePath);
        }
    }
}
