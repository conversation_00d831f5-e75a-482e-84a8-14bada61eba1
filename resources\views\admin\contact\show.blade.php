@extends('layouts.app')

@section('content')
    @include('layouts.navbars.auth.topnav', ['title' => 'Contact Message Details'])

    <div class="container-fluid py-4">
        <!-- Contact Header Card -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center py-3">
                        <h4 class="mb-2">{{ $contact->full_name }}</h4>
                        <div class="mb-2">
                            <span class="text-muted">{{ $contact->email }}</span>
                            @if($contact->phone)
                                <span class="text-muted"> | {{ $contact->phone }}</span>
                            @endif
                        </div>
                        <div class="mb-3">
                            @if($contact->status == 1)
                                <span class="badge bg-success">
                                    <i class="fas fa-eye"></i> Viewed
                                </span>
                            @else
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock"></i> Pending
                                </span>
                            @endif
                        </div>

                        <div class="d-flex justify-content-center gap-2">
                            <a href="{{ route('contact.index') }}" class="btn btn-sm" style="background-color: #8392ab; border-color: #8392ab; color: white;">
                                <i class="fas fa-arrow-left me-1"></i> Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Details Cards -->
        <div class="row">
            <!-- Message Content Card -->
            <div class="col-lg-8">
                <div class="card mb-4">
                    <div class="card-header pb-0">
                        <h6 class="mb-0" style="color: #67748e;">Message Details</h6>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>Subject:</strong>
                            </div>
                            <div class="col-sm-9">
                                <span class="badge bg-info">{{ ucwords(str_replace('-', ' ', $contact->subject)) }}</span>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>Message:</strong>
                            </div>
                            <div class="col-sm-9">
                                <div class="p-3 bg-light rounded">
                                    {!! nl2br(e($contact->message)) !!}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-sm-3">
                                <strong>Received:</strong>
                            </div>
                            <div class="col-sm-9">
                                {{ $contact->created_at->format('l, F j, Y \a\t g:i A') }}
                                <small class="text-muted">({{ $contact->created_at->diffForHumans() }})</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information Card -->
            <div class="col-lg-4">
                <div class="card mb-4">
                    <div class="card-header pb-0">
                        <h6 class="mb-0" style="color: #67748e;">Contact Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Full Name:</strong><br>
                            <span>{{ $contact->full_name }}</span>
                        </div>

                        <div class="mb-3">
                            <strong>Email Address:</strong><br>
                            <a href="mailto:{{ $contact->email }}" class="text-decoration-none">
                                <i class="fas fa-envelope me-1"></i>{{ $contact->email }}
                            </a>
                        </div>

                        @if($contact->phone)
                        <div class="mb-3">
                            <strong>Phone Number:</strong><br>
                            <a href="tel:{{ $contact->phone }}" class="text-decoration-none">
                                <i class="fas fa-phone me-1"></i>{{ $contact->phone }}
                            </a>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
