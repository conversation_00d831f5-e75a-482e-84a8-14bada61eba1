@extends('layouts.app')

@section('content')
    <section class="auth-section">
        <div class="container">
            <div class="auth-container">
                <div class="auth-card">
                    <div class="auth-header">
                        <h2>Create Account</h2>
                        <p>Join us to start managing your trash collection service</p>
                    </div>

                    <form id="register-form" class="auth-form">
                        <div class="form-group">
                            <label for="fullName">Full Name *</label>
                            <div class="input-group">
                                <i class="fas fa-user input-icon"></i>
                                <input type="text" id="fullName" name="fullName" required
                                    placeholder="Enter your full name">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <div class="input-group">
                                <i class="fas fa-envelope input-icon"></i>
                                <input type="email" id="email" name="email" required placeholder="Enter your email">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="password">Password *</label>
                            <div class="input-group">
                                <i class="fas fa-lock input-icon"></i>
                                <input type="password" id="password" name="password" required
                                    placeholder="Create a password">
                                <button type="button" class="password-toggle" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-requirements">
                                <small>Password must be at least 8 characters long</small>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="confirmPassword">Confirm Password *</label>
                            <div class="input-group">
                                <i class="fas fa-lock input-icon"></i>
                                <input type="password" id="confirmPassword" name="confirmPassword" required
                                    placeholder="Confirm your password">
                                <button type="button" class="password-toggle" id="toggleConfirmPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" name="terms" id="terms" required>
                                <span class="checkbox-custom"></span>
                                I agree to the <a href="{{ route('terms') }}" target="_blank">Terms & Conditions</a> and <a
                                    href="{{ route('privacy') }}" target="_blank">Privacy Policy</a>
                            </label>
                        </div>

                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" name="newsletter" id="newsletter">
                                <span class="checkbox-custom"></span>
                                Subscribe to our newsletter for service updates
                            </label>
                        </div>

                        <button type="submit" class="btn btn-primary btn-large auth-submit">
                            <i class="fas fa-user-plus"></i>
                            Create Account
                        </button>

                        <div class="auth-divider">
                            <span class="bg-white">Already have an account? <a href="{{ route('login') }}" class="">Sign
                                    In</a></span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
@endsection
