<?php

namespace App\Rules;

use App\Models\User;
use Illuminate\Contracts\Validation\Rule;

class ValidProvider implements Rule
{
    /**
     * The error message for the failed validation.
     *
     * @var string
     */
    protected $errorMessage;

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        $user = User::where('id', $value)
            ->where('role', '1') // Role 1 indicates provider
            ->with('provider')
            ->first();

        if (!$user) {
            $this->errorMessage = 'The user ID is invalid or does not belong to a verified provider.';
            return false;
        }

        if (!$user->provider) {
            $this->errorMessage = 'The user is not associated with a provider.';
            return false;
        }

        if ($user->provider->status !== 1) {
            $this->errorMessage = 'The provider is not active.';
            return false;
        }

        $hasActiveSubscription = $user->subscriptions->contains(function ($subscription) {
            return $subscription->plan_status === 'active'; // Using the plan_status attribute
        });

        if (!$hasActiveSubscription) {
            $this->errorMessage = 'The provider does not have an active subscription.';
            return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return $this->errorMessage;
    }
}
