@extends('layouts.app')

@section('content')
    @include('layouts.navbars.auth.topnav', ['title' => 'Blog / Create'])

    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-xl-12 col-lg-12 col-md-12">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                        <h5 class="mb-0" style="color: #67748e;">Add New Blog</h5>
                        <a href="{{ route('blog.index') }}" class="btn btn-sm m-0" style="background-color: #67748e; border-color: #67748e; color: white;">
                            <i class="fa fa-arrow-left me-1"></i> Back
                        </a>
                    </div>

                    <div class="card-body">
                        <form action="{{ route('blog.store') }}" method="POST" enctype="multipart/form-data">
                            @csrf

                            <div class="row g-4">
                                <!-- Blog Title -->
                                <div class="col-md-6">
                                    <label class="form-label text-muted">Title <span class="text-danger">*</span></label>
                                    <input type="text" name="name"
                                        class="form-control @error('name') is-invalid @enderror"
                                        value="{{ old('name') }}">
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Feature Image -->
                                <div class="col-md-6">
                                    <label class="form-label text-muted">Feature Image <span
                                            class="text-danger">*</span></label>
                                    <input type="file" name="feature_image" id="featureImageInput"
                                        class="form-control @error('feature_image') is-invalid @enderror">
                                    <div class="mt-2">
                                        <img id="featureImagePreview" src="#" alt="Preview"
                                            class="rounded shadow-sm d-none" style="max-height: 100px; max-width: 100px;">
                                    </div>
                                    @error('feature_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Additional Image -->
                                <div class="col-md-6">
                                    <label class="form-label text-muted">Additional Image</label>
                                    <input type="file" name="image" id="additionalImageInput"
                                        class="form-control @error('image') is-invalid @enderror">
                                    <div class="mt-2">
                                        <img id="additionalImagePreview" src="#" alt="Preview"
                                            class="rounded shadow-sm d-none" style="max-height: 100px; max-width: 100px;">
                                    </div>
                                    @error('image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Author Name -->
                                <div class="col-md-6">
                                    <label class="form-label text-muted">Author Name</label>
                                    <input type="text" name="author_name"
                                        class="form-control @error('author_name') is-invalid @enderror"
                                        value="{{ old('author_name') }}">
                                    @error('author_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Author Image -->
                                <div class="col-md-6">
                                    <label class="form-label text-muted">Author Image</label>
                                    <input type="file" name="author_image" id="authorImageInput"
                                        class="form-control @error('author_image') is-invalid @enderror">
                                    <div class="mt-2">
                                        <img id="authorImagePreview" src="#" alt="Preview"
                                            class="rounded shadow-sm d-none" style="max-height: 100px; max-width: 100px;">
                                    </div>
                                    @error('author_image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Description with CKEditor -->
                                <div class="col-md-12">
                                    <label class="form-label text-muted">Description <span
                                            class="text-danger">*</span></label>
                                    <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror">{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Publish Date -->
                                <div class="col-md-6">
                                    <label class="form-label text-muted">Publish Date</label>
                                    <input type="datetime-local" name="published_at"
                                        class="form-control @error('published_at') is-invalid @enderror"
                                        value="{{ old('published_at') }}">
                                    @error('published_at')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <!-- Status -->
                                <div class="col-md-6">
                                    <label class="form-label text-muted">Status</label>
                                    <select name="status" class="form-select">
                                        <option value="1" {{ old('status', 1) == 1 ? 'selected' : '' }}>Active
                                        </option>
                                        <option value="0" {{ old('status') == 0 ? 'selected' : '' }}>Inactive</option>
                                    </select>
                                </div>

                                <!-- Submit Button -->
                                <div class="col-12 text-end">
                                    <button type="submit" class="btn px-4" style="background-color: #67748e; border-color: #67748e; color: white;">
                                        <i class="fa fa-save me-1"></i> Save Blog
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CKEditor -->
    <script src="https://cdn.ckeditor.com/ckeditor5/41.1.0/classic/ckeditor.js"></script>
    <script>
        class MyUploadAdapter {
            constructor(loader) {
                this.loader = loader;
            }

            upload() {
                return this.loader.file.then(file => {
                    return new Promise((resolve, reject) => {
                        const data = new FormData();
                        data.append('upload', file);
                        data.append('_token', '{{ csrf_token() }}');

                        fetch('{{ route('ckeditor.upload') }}', {
                                method: 'POST',
                                body: data
                            })
                            .then(response => response.json())
                            .then(result => resolve({
                                default: result.url
                            }))
                            .catch(error => reject(error.message));
                    });
                });
            }

            abort() {}
        }

        function CustomUploadAdapterPlugin(editor) {
            editor.plugins.get('FileRepository').createUploadAdapter = (loader) => new MyUploadAdapter(loader);
        }

        ClassicEditor
            .create(document.querySelector('#description'), {
                extraPlugins: [CustomUploadAdapterPlugin],
                toolbar: [
                    'heading', '|',
                    'bold', 'italic', 'bulletedList', 'numberedList', '|',
                    'blockQuote', 'insertTable', 'undo', 'redo', 'imageUpload'
                ]
            })
            .catch(error => {
                console.error(error);
            });

        // ClassicEditor
        //     .create(document.querySelector('#description'), {
        //         extraPlugins: [CustomUploadAdapterPlugin],
        //     })
        //     .catch(error => {
        //         console.error(error);
        //     });
    </script>

    <!-- Image Preview -->
    <script>
        function readAndPreview(input, previewId) {
            const file = input.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.getElementById(previewId);
                    img.src = e.target.result;
                    img.classList.remove('d-none');
                };
                reader.readAsDataURL(file);
            }
        }

        document.addEventListener("DOMContentLoaded", function() {
            document.getElementById('featureImageInput')?.addEventListener('change', function() {
                readAndPreview(this, 'featureImagePreview');
            });

            document.getElementById('additionalImageInput')?.addEventListener('change', function() {
                readAndPreview(this, 'additionalImagePreview');
            });

            document.getElementById('authorImageInput')?.addEventListener('change', function() {
                readAndPreview(this, 'authorImagePreview');
            });
        });
    </script>
@endsection
