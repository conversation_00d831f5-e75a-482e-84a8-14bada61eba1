<?php

namespace App\Models;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Service extends Model
{
    use HasFactory;

    protected $primaryKey = 'service_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'plan_id',
        'name',
        'description',
        'status',
        'sort_order',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    protected static function booted(): void
    {
        static::creating(function ($service) {
            if (empty($service->service_id)) {
                $service->service_id = (string) Str::uuid();
            }
        });
    }

    // Scope for active services
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }

    // Relationship with plan
    public function plan()
    {
        return $this->belongsTo(Plan::class, 'plan_id', 'plans_id');
    }
}
