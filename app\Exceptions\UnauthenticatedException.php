<?php

namespace App\Exceptions;
use Illuminate\Http\Request;
use Exception;

class UnauthenticatedException extends Exception
{
    public function render(Request $request)
    {
        // Check if the request is an API request (expects JSON)
        if ($request->is('api/*')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated, invalid token error.',
            ], 200);
        }

        // Redirect to the login page for web requests
        return redirect()->route('login')->with('error', 'Please re-attempt login');
    }
}
