<?php

namespace App\Http\Controllers\Guest;

use App\Http\Controllers\Controller;
use App\Models\Plan;

class MainController extends Controller
{
    public function index()
    {
        return view('guest.index');
    }

    public function about()
    {
        return view('guest.about');
    }

    public function terms()
    {
        return view('guest.terms');
    }

    public function privacy()
    {
        return view('guest.privacy');
    }

    public function pricing()
    {
        $plans = Plan::with('services')
            ->where('status', 1)
            ->orderBy('sort_order', 'asc')
            ->orderBy('is_popular', 'desc')
            ->get();
        return view('guest.pricing', compact('plans'));
    }

    public function contact()
    {
        return view('guest.contact');
    }

    public function news()
    {
        return view('guest.news');
    }
}
