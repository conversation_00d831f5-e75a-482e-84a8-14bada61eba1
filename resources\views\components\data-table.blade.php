<div class="table-responsive p-0">
    <table id="{{ $id }}" class="table align-items-center mb-0 {{ $classes ?? '' }}" style="width: 100%;">
        <thead>
            <tr>
                {{ $header }}
            </tr>
        </thead>
        <tbody>
            {{ $slot }}
        </tbody>
    </table>
</div>

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/link/dataTables.bootstrap5.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/link/responsive.bootstrap5.min.css') }}">

    <style>
        .dataTables_length select {
            min-width: 4.2rem !important;
        }

        .dataTables_wrapper td {
            vertical-align: middle;
        }

        .paginate_button {
            padding: 0px 1px;
        }
    </style>
@endpush


@push('js')
    <script src="{{ asset('assets/script/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('assets/script/dataTables.bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/script/dataTables.responsive.min.js') }}"></script>
    <script src="{{ asset('assets/script/responsive.bootstrap5.min.js') }}"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (!$.fn.dataTable.isDataTable('#{{ $id }}')) {
                $('#{{ $id }}').DataTable({
                    // responsive: true,
                    scrollX: true,
                    paging: true,
                    searching: true,
                    info: true,
                    autoWidth: false,
                    pageLength: 10,
                    lengthMenu: [10, 50, 100, 500],
                    processing: true,
                    serverSide: true,
                    ajax: "{{ $ajax }}",
                    columns: @json($columns),
                    order: @json($order),
                    language: {
                        paginate: {
                            previous: '<i class="fas fa-angle-left"></i>',
                            next: '<i class="fas fa-angle-right"></i>'
                        },
                        lengthMenu: "Show _MENU_ entries",
                        search: "_INPUT_",
                        searchPlaceholder: "Search..."
                    },
                    dom: "<'row mb-3'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
                        "<'table-responsive'tr>" +
                        "<'row mt-3'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>"
                });
            } else {
                $('#{{ $id }}').DataTable().ajax.reload();
            }
        });
    </script>
@endpush







{{-- <div class="table-responsive p-0">
    <table id="{{ $id }}" class="table align-items-center mb-0 {{ $classes ?? '' }}" style="width: 100%;">
        <thead>
            <tr>
                {{ $header }}
            </tr>
        </thead>
        <tbody>
            {{ $slot }}
        </tbody>
    </table>
</div>

@push('css')
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.4.1/css/responsive.bootstrap5.min.css">
    <style>
        .dataTables_length select {
            min-width: 4.2rem !important;
        }

        .dataTables_wrapper td {
            padding-left: 26px;
            vertical-align: middle;
        }
    </style>
@endpush

@push('js')
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.4.1/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.4.1/js/responsive.bootstrap5.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (!$.fn.dataTable.isDataTable('#{{ $id }}')) {
                $('#{{ $id }}').DataTable({
                    responsive: true,
                    responsive: false,
                    scrollX: true,
                    paging: true,
                    searching: true,
                    info: true,
                    autoWidth: false,
                    pageLength: 10,
                    lengthMenu: [10, 50, 100, 500],
                    processing: true,
                    serverSide: true,
                    ajax: "{{ $ajax }}", // Dynamic AJAX URL
                    columns: @json($columns),
                    order: @json($order), // Dynamic order field
                    language: {
                        paginate: {
                            previous: "<",
                            next: ">"
                        }
                    }
                });
            }
        });
    </script>
@endpush --}}
