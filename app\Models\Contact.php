<?php

namespace App\Models;

use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Contact extends Model
{
    use HasFactory;

    protected $primaryKey = 'contacts_id';
    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'phone',
        'subject',
        'message',
        'status',
    ];

    protected $casts = [
        'status' => 'integer',
    ];

    protected static function booted(): void
    {
        static::creating(function ($contact) {
            if (empty($contact->contacts_id)) {
                $contact->contacts_id = (string) Str::uuid();
            }
        });
    }

    // Scope for pending contacts
    public function scopePending($query)
    {
        return $query->where('status', 0);
    }

    // Scope for viewed contacts
    public function scopeViewed($query)
    {
        return $query->where('status', 1);
    }

    // Accessor for full name
    public function getFullNameAttribute()
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    // Accessor for status text
    public function getStatusTextAttribute()
    {
        return $this->status == 1 ? 'Viewed' : 'Pending';
    }
}
