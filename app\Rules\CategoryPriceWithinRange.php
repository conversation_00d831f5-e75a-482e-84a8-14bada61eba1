<?php

namespace App\Rules;

use App\Models\Category;
use Illuminate\Contracts\Validation\Rule;

class CategoryPriceWithinRange implements Rule
{
    protected $categoryId;
    protected $startRange;
    protected $endRange;
    protected $errorMessage;

    /**
     * Create a new rule instance.
     *
     * @param int $categoryId
     */
    public function __construct($categoryId)
    {
        $this->categoryId = $categoryId;
        $this->errorMessage = "The price is not within the allowed range.";
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $value = (integer) $value;
        // Find the category by ID
        $category = Category::find($this->categoryId);
        // If category is not found
        if (!$category) {
            $this->errorMessage = "The selected category does not exist.";
            return false;
        }
        
        // If category is not active
        if ($category->status != 1) {
            $this->errorMessage = "The category is not active.";
            return false;
        }
        
        // Retrieve the start and end range from the category
        $this->startRange = $category->start_range;
        $this->endRange = $category->end_range;
        
        // Check if the price is within the valid range
        // dd($value, '>=', $this->startRange, "  ", $value, '<=', $this->endRange);
        if ($value >= $this->startRange && $value <= $this->endRange) {
            return true;
        }
        
        // Set the error message if price is not within the range
        $this->errorMessage = "The price must be between {$this->startRange} and {$this->endRange}.";
        return false;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return $this->errorMessage;
    }
}
