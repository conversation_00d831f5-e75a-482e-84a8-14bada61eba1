@extends('layouts.app')

@section('content')
    <section class="auth-section">
        <div class="container">
            <div class="auth-container">
                <div class="auth-card">
                    <div class="auth-header">
                        <h2>Welcome Back</h2>
                        <p>Sign in to your account to manage your trash collection service</p>
                    </div>

                    <form id="login-form" class="auth-form" method="POST" action="{{ route('login.post') }}">
                        @csrf

                        <div class="form-group">
                            <label for="email">Email Address *</label>
                            <div class="input-group">
                                <i class="fas fa-envelope input-icon"></i>
                                <input type="email" id="email" name="email" value="{{ old('email') }}"
                                    class="@error('email') input-error @enderror" placeholder="Enter your email" required>
                            </div>
                            @error('email')
                                <span class="error-message">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-group">
                            <label for="password">Password *</label>
                            <div class="input-group">
                                <i class="fas fa-lock input-icon"></i>
                                <input type="password" id="password" name="password"
                                    class="@error('password') input-error @enderror" placeholder="Enter your password"
                                    required>
                                <button type="button" class="password-toggle" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            @error('password')
                                <span class="error-message">{{ $message }}</span>
                            @enderror
                        </div>

                        <div class="form-options">
                            {{-- <label class="checkbox-label">
                                <input type="checkbox" name="remember" id="remember">
                                <span class="checkbox-custom"></span>
                                Remember me
                            </label> --}}
                            <a href="{{-- {{ route('password.request') }} --}}" class="forgot-link">Forgot Password?</a>
                        </div>

                        <button type="submit" class="btn btn-primary btn-large auth-submit">
                            <i class="fas fa-sign-in-alt"></i>
                            Sign In
                        </button>

                        <div class="auth-divider">
                            <span class="bg-white">Don't have an account? <a href="{{ route('register') }}">Create
                                    Account</a></span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <script>
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    </script>
@endsection
