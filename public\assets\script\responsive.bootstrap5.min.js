/*! Bootstrap 5 integration for DataTables' Responsive
 * © SpryMedia Ltd - datatables.net/license
 */
!function(o){var a,n;"function"==typeof define&&define.amd?define(["jquery","datatables.net-bs5","datatables.net-responsive"],function(e){return o(e,window,document)}):"object"==typeof exports?(a=require("jquery"),n=function(e,d){d.fn.dataTable||require("datatables.net-bs5")(e,d),d.fn.dataTable.Responsive||require("datatables.net-responsive")(e,d)},"undefined"!=typeof window?module.exports=function(e,d){return e=e||window,d=d||a(e),n(e,d),o(d,e,e.document)}:(n(window,a),module.exports=o(a,window,window.document))):o(jQuery,window,document)}(function(i,e,d,o){"use strict";var s,a=i.fn.dataTable,n=a.Responsive.display,l=n.modal,r=i('<div class="modal fade dtr-bs-modal" role="dialog"><div class="modal-dialog" role="document"><div class="modal-content"><div class="modal-header"><button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button></div><div class="modal-body"/></div></div></div>'),u=e.bootstrap;return a.Responsive.bootstrap=function(e){u=e},n.modal=function(t){return s=s||new u.Modal(r[0]),function(e,d,o){var a,n;i.fn.modal?d||(t&&t.header&&(n=(a=r.find("div.modal-header")).find("button").detach(),a.empty().append('<h4 class="modal-title">'+t.header(e)+"</h4>").append(n)),r.find("div.modal-body").empty().append(o()),r.appendTo("body").modal(),s.show()):l(e,d,o)}},a});