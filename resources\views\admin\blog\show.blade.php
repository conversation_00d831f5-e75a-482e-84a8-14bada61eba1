@extends('layouts.app')

@section('content')
    @include('layouts.navbars.auth.topnav', ['title' => 'Blog / View'])
    
    <style>
        .blog-description img {
            max-width: 100% !important;
            height: auto !important;
            display: block;
            margin: 10px 0;
            object-fit: contain;
        }

        .blog-description iframe {
            max-width: 100%;
        }
    </style>

    <div class="container-fluid py-4">
        <div class="row justify-content-center">
            <div class="col-xl-12 col-lg-12 col-md-12">
                <div class="card shadow-sm border-0">
                    <div class="card-header bg-white border-bottom d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 ttl">Blog Details</h5>
                        <a href="{{ route('blog.index') }}" class="btn btn-sm m-0 bdr">
                            <i class="fa fa-arrow-left me-1"></i> Back
                        </a>
                    </div>

                    <div class="card-body">
                        <div class="p-4 rounded shadow-sm border bg-white">
                            <div class="row g-4">
                                {{-- Title --}}
                                <div class="col-md-6">
                                    <div class="border-start ps-3">
                                        <small class="text-muted">Title</small>
                                        <h6 class="mb-0 text-primary">{{ $blog->name ?? '-' }}</h6>
                                    </div>
                                </div>

                                {{-- Slug --}}
                                <div class="col-md-6">
                                    <div class="border-start ps-3">
                                        <small class="text-muted">Slug</small>
                                        <h6 class="mb-0 text-dark">{{ $blog->slug ?? '-' }}</h6>
                                    </div>
                                </div>

                                {{-- Feature Image --}}
                                <div class="col-md-6">
                                    <div class="border-start ps-3">
                                        <small class="text-muted">Feature Image</small>
                                        @if ($blog->feature_image)
                                            <div class="mt-2">
                                                <img src="{{ asset($blog->feature_image) }}" width="150"
                                                    class="rounded shadow-sm">
                                            </div>
                                        @else
                                            <p class="text-muted">-</p>
                                        @endif
                                    </div>
                                </div>

                                {{-- Additional Image --}}
                                <div class="col-md-6">
                                    <div class="border-start ps-3">
                                        <small class="text-muted">Additional Image</small>
                                        @if ($blog->image)
                                            <div class="mt-2">
                                                <img src="{{ asset($blog->image) }}" width="150"
                                                    class="rounded shadow-sm">
                                            </div>
                                        @else
                                            <p class="text-muted">-</p>
                                        @endif
                                    </div>
                                </div>

                                {{-- Author Name --}}
                                <div class="col-md-6">
                                    <div class="border-start ps-3">
                                        <small class="text-muted">Author Name</small>
                                        <h6 class="mb-0">{{ $blog->author_name ?? '-' }}</h6>
                                    </div>
                                </div>

                                {{-- Author Image --}}
                                <div class="col-md-6">
                                    <div class="border-start ps-3">
                                        <small class="text-muted">Author Image</small>
                                        @if ($blog->author_image)
                                            <div class="mt-2">
                                                <img src="{{ asset($blog->author_image) }}" width="100"
                                                    class="rounded shadow-sm">
                                            </div>
                                        @else
                                            <p class="text-muted">-</p>
                                        @endif
                                    </div>
                                </div>

                                {{-- Status --}}
                                <div class="col-md-6">
                                    <div class="border-start ps-3">
                                        <small class="text-muted">Status</small>
                                        <div class="mt-1">
                                            <span
                                                class="bdr badge px-3 py-1 {{ $blog->status ? 'bg-success' : 'bg-secondary' }}">
                                                {{ $blog->status ? 'Active' : 'Inactive' }}
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                {{-- Published At --}}
                                <div class="col-md-6">
                                    <div class="border-start ps-3">
                                        <small class="text-muted">Published At</small>
                                        <h6 class="mb-0">
                                            {{ $blog->published_at ? \Carbon\Carbon::parse($blog->published_at)->format('d M, Y h:i A') : '-' }}
                                        </h6>
                                    </div>
                                </div>

                                {{-- Description --}}
                                <div class="col-12">
                                    <div class="p-3 rounded bg-light border blog-description">
                                        <small class="text-muted d-block mb-2">Description</small>
                                        <div class="text-dark lh-base">
                                            {!! $blog->description !!}
                                        </div>
                                    </div>
                                </div>

                                {{-- Created At --}}
                                <div class="col-12">
                                    <div class="border-start ps-3">
                                        <small class="text-muted">Created At</small>
                                        <h6 class="mb-0 fw-semibold">
                                            {{ $blog->created_at ? $blog->created_at->format('d M, Y h:i A') : '-' }}
                                        </h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
