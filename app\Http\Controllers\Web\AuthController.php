<?php

namespace App\Http\Controllers\Web;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rules\Password;

class AuthController extends Controller
{
    public function index()
    {
        return view("auth.login");
    }

    public function login(Request $request)
    {
        try {
            $credentials = $request->validate([
                'email' => ['required', 'email', 'exists:users,email'],
                'password' => [
                    'required',
                    Password::min(8)->mixedCase()->letters()->numbers()->symbols()->max(20),
                ],
            ]);

            logger()->info('Login attempt', ['email' => $request->email]);

            if (Auth::attempt($credentials)) {
                $request->session()->regenerate();

                Log::info('User login successful', [
                    // Auth Info (if already authenticated)
                    'user_id'        => Auth::id(),
                    'email'          => $request->email ?? null,

                    // Request Info
                    'ip_address'     => $request->ip(),
                    'user_agent'     => $request->userAgent(),
                    'session_id'     => session()->getId(),
                    'request_url'    => $request->fullUrl(),
                    'request_method' => $request->method(),

                    // Server Info
                    'host'           => $request->getHost(),
                    'server_port'    => $request->getPort(),
                    'referer'        => $request->headers->get('referer'),

                    // Device/Browser Info (basic parsing)
                    // 'platform'       => get_platform($request->userAgent()),
                    // 'browser'        => get_browser_name($request->userAgent()),

                    // Geo info if using IP geolocation services (optional)
                    // 'country'     => geoip()->getLocation($request->ip())->country ?? null,

                    // Timestamp
                    'login_time'     => now()->toDateTimeString(),
                ]);

                return redirect()->route('dashboard')->with('success', 'Login successful!');
            }

            logger()->warning('Login failed', ['email' => $request->email]);

            return back()->withErrors([
                'email' => 'The provided credentials do not match our records.',
            ])->onlyInput('email');
        } catch (Exception $e) {
            Log::error('Login error occurred', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => $request->ip(),
                'time' => now()->toDateTimeString(),
            ]);

            return back()->withErrors([
                'email' => 'The provided email address is not registered.',
            ])->onlyInput('email');
        }
    }

    public function logout(Request $request)
    {
        $user = Auth::user();

        Auth::logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        Session::forget('some_session_key');

        Log::info('User logged out', [
            'user_uuid' => optional($user)->users_id ?? optional($user)->id,
            'email'     => optional($user)->email,
            'ip'        => $request->ip(),
            'user_agent' => $request->userAgent(),
            'logged_out_at' => now()->toDateTimeString(),
        ]);

        return redirect()->route('login')->with('success', 'You have been logged out successfully.');
    }

    public function register()
    {
        return view("auth.register");
    }
}
